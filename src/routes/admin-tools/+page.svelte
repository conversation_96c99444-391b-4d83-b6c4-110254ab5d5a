<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';

	import * as Dialog from '$lib/components/ui/dialog';
	import * as Alert from '$lib/components/ui/alert';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Warning, TestTube, Buildings } from 'phosphor-svelte';

	const { data }: PageProps = $props();

	// Redirect if not in development mode (client-side check)

	const form = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const ribaForm = superForm(data.ribaForm, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance } = form;
	const { form: ribaFormData, enhance: ribaEnhance } = ribaForm;

	let showConfirmDialog = $state(false);
	let showRibaConfirmDialog = $state(false);
</script>

<svelte:head>
	<title>Admin Tools - Development Only</title>
</svelte:head>

<div class="container mx-auto max-w-4xl py-8">
	<div class="mb-8">
		<div class="mb-4 flex items-center gap-3">
			<TestTube class="h-8 w-8 text-orange-500" />
			<h1 class="mb-0 text-3xl font-bold">Admin Tools</h1>
			<div class="rounded-full bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800">
				Development Only
			</div>
		</div>
		<p class="text-muted-foreground">
			Administrative tools for development and testing purposes. These tools are only available in
			development mode and should never be used in production.
		</p>
	</div>

	<!-- Warning Alert -->
	<Alert.Root class="mb-8 border-orange-200 bg-orange-50">
		<Warning class="h-4 w-4 text-orange-600" />
		<Alert.Title class="text-orange-800">Development Environment Only</Alert.Title>
		<Alert.Description class="text-orange-700">
			These tools are designed for development and testing purposes only. They will not be available
			in production environments. Use with caution as they may modify or create test data in your
			database.
		</Alert.Description>
	</Alert.Root>

	<!-- Demo Project Generation Tool -->
	<!-- <div class="bg-card rounded-lg border p-6">
		<div class="mb-4 flex items-center gap-3">
			<Database class="h-6 w-6 text-blue-500" />
			<h2 class="text-xl font-semibold">Generate Demo Project Data</h2>
		</div>

		<p class="text-muted-foreground mb-6">
			Creates a comprehensive test project under the Unity client with staged budget data, risk
			register entries, and approved changes across 4 project stages. This provides realistic test
			data for all project management features.
		</p>

		<div class="bg-muted/50 mb-6 rounded border p-4">
			<h3 class="mb-2 font-medium">What this tool does:</h3>
			<ul class="text-muted-foreground space-y-1 text-sm">
				<li>• Creates a new test project under Unity client</li>
				<li>• Generates 4 project stages with budget data</li>
				<li>• Creates budget line items with random values across WBS hierarchy</li>
				<li>• Takes budget snapshots at each stage gateway</li>
				<li>• Generates 15-50 realistic risk register entries with varied statuses</li>
				<li>• Creates approved changes (some linked to high-probability risks)</li>
				<li>• Links risks and changes to WBS items where appropriate</li>
				<li>• Provides comprehensive test data for all project features</li>
			</ul>
		</div>

		<Button onclick={() => (showConfirmDialog = true)} class="w-full sm:w-auto">
			Generate Demo Project Data
		</Button>
	</div> -->

	<!-- RIBA Demo Project Generation Tool -->
	<div class="bg-card rounded-lg border p-6">
		<div class="mb-4 flex items-center gap-3">
			<Buildings class="h-6 w-6 text-green-500" />
			<h2 class="text-xl font-semibold">Generate RIBA Demo Project Data</h2>
		</div>

		<p class="text-muted-foreground mb-6">
			Creates a comprehensive RIBA-staged project under the Aurora organization with detailed budget
			progression through all 6 RIBA stages (0-5). Includes vendors, purchase orders, work packages,
			invoices, risks, and approved changes with realistic data relationships.
		</p>

		<div class="bg-muted/50 mb-6 rounded border p-4">
			<h3 class="mb-2 font-medium">What this tool does:</h3>
			<ul class="text-muted-foreground space-y-1 text-sm">
				<li>• Creates a new client and project under Aurora organization</li>
				<li>• Generates all 6 RIBA stages (0-5) with progressive budget detail</li>
				<li>• Stage 1: High-level acquisition/construction split</li>
				<li>• Stage 2-5: Progressive budget breakdown to level 4 WBS detail</li>
				<li>• Creates 30 vendors with purchase orders and work packages</li>
				<li>• Generates invoices linked to purchase orders</li>
				<li>• Creates 10 risk register entries with potential approved changes</li>
				<li>• Uses ICMS v3 WBS library for realistic cost structure</li>
			</ul>
		</div>

		<Button onclick={() => (showRibaConfirmDialog = true)} class="w-full sm:w-auto">
			Generate RIBA Demo Project Data
		</Button>
	</div>

	<!-- Confirmation Dialog -->
	<Dialog.Root bind:open={showConfirmDialog}>
		<Dialog.Content class="sm:max-w-md">
			<Dialog.Header>
				<Dialog.Title>Confirm Demo Project Generation</Dialog.Title>
				<Dialog.Description>
					This will create a new test project with comprehensive data including budget, risks, and
					approved changes. Make sure you want to proceed.
				</Dialog.Description>
			</Dialog.Header>

			<form method="POST" action="?/generateDemoProject" use:enhance>
				<div class="space-y-4">
					<div class="flex items-center space-x-2">
						<Checkbox id="confirm-checkbox" name="confirm" bind:checked={$formData.confirm} />
						<label for="confirm-checkbox" class="text-sm font-normal">
							I understand this will create test data in the database
						</label>
					</div>
				</div>

				<Dialog.Footer class="mt-6">
					<Button type="button" variant="outline" onclick={() => (showConfirmDialog = false)}>
						Cancel
					</Button>
					<Button
						type="submit"
						disabled={!$formData.confirm}
						onclick={() => (showConfirmDialog = false)}
					>
						Generate Demo Project Data
					</Button>
				</Dialog.Footer>
			</form>
		</Dialog.Content>
	</Dialog.Root>

	<!-- RIBA Confirmation Dialog -->
	<Dialog.Root bind:open={showRibaConfirmDialog}>
		<Dialog.Content class="sm:max-w-md">
			<Dialog.Header>
				<Dialog.Title>Confirm RIBA Demo Project Generation</Dialog.Title>
				<Dialog.Description>
					This will create a new RIBA-staged project with comprehensive data including progressive
					budget detail, vendors, purchase orders, and risk management data.
				</Dialog.Description>
			</Dialog.Header>

			<form method="POST" action="?/generateRibaDemoProject" use:ribaEnhance>
				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="total-budget">Total Budget (£)</Label>
						<Input
							id="total-budget"
							name="totalBudget"
							type="number"
							min="100000"
							max="10000000000"
							step="1000"
							bind:value={$ribaFormData.totalBudget}
							placeholder="1000000000"
						/>
					</div>
					<div class="flex items-center space-x-2">
						<Checkbox
							id="riba-confirm-checkbox"
							name="confirm"
							bind:checked={$ribaFormData.confirm}
						/>
						<label for="riba-confirm-checkbox" class="text-sm font-normal">
							I understand this will create RIBA test data in the database
						</label>
					</div>
				</div>

				<Dialog.Footer class="mt-6">
					<Button type="button" variant="outline" onclick={() => (showRibaConfirmDialog = false)}>
						Cancel
					</Button>
					<Button
						type="submit"
						disabled={!$ribaFormData.confirm}
						onclick={() => (showRibaConfirmDialog = false)}
					>
						Generate RIBA Demo Project Data
					</Button>
				</Dialog.Footer>
			</form>
		</Dialog.Content>
	</Dialog.Root>
</div>
